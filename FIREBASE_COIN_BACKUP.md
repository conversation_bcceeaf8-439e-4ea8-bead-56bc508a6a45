# Hệ thống Backup Coin với Firebase (Đơn giản & <PERSON><PERSON><PERSON> quả)

## Tổng quan

Hệ thống backup coin đơn giản với 2 nguyên tắc chính:
1. **Khi lưu data vào PowerSync backend** → cũng lưu vào Firebase
2. **Khi lỗi kết nối PowerSync** → chuyển qua dùng coin từ Firebase

## Cách hoạt động

### 1. Dual Write Strategy
- Mỗi khi `updateCoin()` thành công trong PowerSync → tự động backup lên Firebase
- Nếu PowerSync fail → fallback ngay lập tức sang Firebase
- Sử dụng FirestoreUtils hiện có để tương thích hoàn toàn

### 2. Write Operations (updateCoin)
```kotlin
// UserRepository.updateCoin()
try {
    // 1. Cập nhật PowerSync
    database.writeTransaction { ... }

    // 2. Luôn backup lên Firebase sau khi PowerSync thành công
    FirestoreUtils.updateCoinsForUser(userId, coin)

} catch (PowerSyncException) {
    // 3. PowerSync fail → fallback sang Firebase
    FirestoreUtils.updateCoinsForUser(userId, coin)
    PrefAssist.setLong(PrefConst.TOTAL_COIN_BALANCE, coin) // Update UI
}
```

### 3. Read Operations (getCoin)
```kotlin
// UserRepository.getCoin()
try {
    // 1. Thử lấy từ PowerSync trước
    return database.getOptional("SELECT coin FROM users")
} catch (Exception) {
    // 2. PowerSync lỗi → lấy từ Firebase
    return getFirebaseCoin()
}
```

## Ưu điểm của approach này

### 1. Đơn giản & Reliable
- Không có background jobs phức tạp
- Không có conflict resolution logic
- Fallback ngay lập tức khi cần

### 2. Performance tốt
- Chỉ write Firebase khi cần thiết
- Không có polling hoặc periodic sync
- Minimal overhead

### 3. User Experience mượt mà
- UI luôn responsive
- Không có loading states phức tạp
- Transparent fallback

## Auto Sync trong AppDatabase

### 1. startAutoFirebaseSync()
- Bắt đầu background job khi init database
- Chạy trong CoroutineScope riêng biệt
- Tự động dừng khi logout

### 2. performFirebaseSync()
- So sánh coin giữa PowerSync và Firebase
- Sử dụng coin cao hơn để resolve conflicts
- Cập nhật cả hai hệ thống để đồng bộ

### 3. stopAutoFirebaseSync()
- Tự động gọi khi logout
- Dọn dẹp background jobs

## Methods mới trong UserRepository

### 1. backupCoinToFirebase(coin: Long)
- Backup coin lên Firebase sử dụng FirestoreUtils
- Private method, tự động gọi trong updateCoin()

### 2. syncCoinFromFirebase()
- Sync coin từ Firebase về local
- Thử cập nhật lại PowerSync nếu có thể
- Public method cho manual sync

## Tích hợp với code hiện tại

### 1. Không breaking changes
- Sử dụng FirestoreUtils hiện có
- Không thay đổi API của CoinViewModel
- Tương thích với logic coin hiện tại

### 2. Error handling
- PowerSync fail → Firebase backup
- Firebase fail → Local storage
- UI luôn được cập nhật

### 3. User experience
- Hoàn toàn transparent (user không cần biết gì)
- Không có UI components phức tạp
- Tự động xử lý mọi trường hợp

## Testing

### 1. Test PowerSync failure
```kotlin
// Ngắt kết nối PowerSync
AppDatabase.getInstance().disconnectAndClear()

// Thử cập nhật coin
coinViewModel.updateCoinBalance(100, "test")

// Kiểm tra coin có được backup lên Firebase không
```

### 2. Test Firebase fallback
```kotlin
// Ngắt internet
// Kiểm tra app có hoạt động với local storage không
```

## Monitoring

### Debug logs
- "PowerSync updateCoin thành công/thất bại"
- "Firebase backup thành công/thất bại"
- "Auto Firebase sync started/stopped"
- "Firebase sync - PowerSync: X, Firebase: Y"
- "Updated PowerSync/Firebase with coin: Z"

### User feedback
- Hoàn toàn transparent, không có UI feedback
- Error handling tự động trong background
- User chỉ thấy coin balance luôn chính xác

## Best practices

### 1. Performance
- Backup chỉ khi cần thiết
- Sử dụng existing FirestoreUtils
- Không block UI thread

### 2. Data consistency
- PowerSync là primary source
- Firebase là backup
- Local storage là fallback cuối cùng

### 3. User experience
- Automatic backup transparent
- Manual sync option available
- Clear visual feedback

## Troubleshooting

### Common issues

1. **Coin không sync**
   - Kiểm tra Firebase connection
   - Kiểm tra user authentication
   - Xem debug logs

2. **Manual sync không hoạt động**
   - Kiểm tra user đã đăng nhập chưa
   - Kiểm tra FirestoreUtils.syncCoinsFirestore()

3. **PowerSync và Firebase không consistent**
   - Chạy manual sync
   - Kiểm tra conflict resolution logic

### Debug commands
```kotlin
// Kiểm tra auto sync job
AppDatabase.getInstance().autoSyncJob?.isActive

// Force sync (for testing)
AppDatabase.getInstance().performFirebaseSync()

// Check coin consistency
val powerSyncCoin = userRepository.getCoin()
val firebaseCoin = PrefAssist.getLong(PrefConst.TOTAL_COIN_BALANCE, 0L)
```

## Future improvements
- Real-time sync với Firebase listeners
- Conflict resolution UI
- Sync status indicator
- Offline queue management
